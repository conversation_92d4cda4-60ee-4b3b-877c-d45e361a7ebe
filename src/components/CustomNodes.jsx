
import { Handle, Position } from '@xyflow/react'

export const ApprovalNode = ({ data }) => (
  <div
    className="bg-green-100 border border-green-500 rounded p-3 shadow-md min-w-[150px] text-center relative"
    onContextMenu={(e) => {
      e.preventDefault()
      data.onRightClick(e)
    }}
  >
    <div className="font-semibold mb-2">🟢 Approval</div>
    <div>{data.label}</div>
    <button
      onClick={data.onApprove}
      className="mt-2 px-2 py-1 bg-green-500 text-white text-sm rounded"
    >
      Approve
    </button>
    <Handle type="target" position={Position.Left} />
    <Handle type="source" position={Position.Right} />
  </div>
)

export const DecisionNode = ({ data }) => (
  <div
    className="bg-yellow-100 border border-yellow-500 rounded p-3 shadow-md min-w-[150px] text-center relative"
    onContextMenu={(e) => {
      e.preventDefault()
      data.onRightClick(e)
    }}
  >
    <div className="font-semibold mb-2">🔶 Decision</div>
    <div>{data.label}</div>
    <button
      onClick={data.onDecision}
      className="mt-2 px-2 py-1 bg-yellow-500 text-white text-sm rounded"
    >
      Decide
    </button>
    <Handle type="target" position={Position.Left} />
    <Handle type="source" position={Position.Right} />
  </div>
)
